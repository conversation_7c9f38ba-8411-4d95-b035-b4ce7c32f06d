% function plot_spectrogram(data, fs, position, rows, cols, index, title_text)
%     w = 0.05;
%     win = round(fs * w);
%     ov = round(fs * w * 0.9);
%     nfft = round(fs * 0.5);
%     [S, F, T, P] = spectrogram(data, win, ov, nfft, fs);
% 
%     figure('Position', position); % 设置图框位置和大小，[left, bottom, width, height]
%     subplot(rows, cols, index);
%     surf(T, F, 10 * log10(P), 'edgecolor', 'none');
%     axis tight;
%     view(0, 90);
%     % caxis([-90 -50]);
%     caxis([-90 -50]);
%     colorbar;
%     colormap(inferno);
%     ax = gca;
%     ax.FontSize = 16; % 设置刻度尺文字大小
%     ax.FontName = 'Times New Roman'; % 设置刻度尺字体
%     ax.FontWeight = "bold";
%     xlabel('Time(s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
%     ylabel('Freq.(Hz)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
%     title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
% end

function plot_spectrogram_bs(data, fs, title_text)
    w = 0.05; % 窗口宽度设置为0.05秒
    win = round(fs * w); % 将窗口宽度转换为采样点数
    ov = round(fs * w * 0.9); % 设置90%的窗口重叠
    nfft = round(fs * 0.5); % 设置FFT点数为采样率的一半
    
    % 计算频谱图
    [S, F, T, P] = spectrogram(data, win, ov, nfft, fs);
    
    % 绘制频谱图
    surf(T, F, 10 * log10(P), 'edgecolor', 'none'); % 绘制功率谱密度的表面图
    axis tight;
    view(0, 90); % 设置为俯视视角
    caxis([-90 -50]); % 控制颜色范围
    colorbar;
    colormap(inferno); % 使用'inferno'颜色映射
    
    % 设置坐标轴及标题
    ax = gca;
    ax.FontSize = 16;
    ax.FontName = 'Times New Roman';
    ax.FontWeight = "bold";
    xlabel('Time (s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Freq (Hz)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
end


