% function plot_waveform_bs(t, data, title_text)
%     plot(t, data); % 直接绘制波形
%     hold on; % 保持当前图形，以便添加新的绘图元素
% 
%     % 添加两条红色水平线
%     xrange = [min(t) max(t)]; % 获取x轴范围
%     plot(xrange, [-0.2 -0.2], 'r-', 'LineWidth', 1.5); % 画-0.2水平线
%     plot(xrange, [0.2 0.2], 'r-', 'LineWidth', 1.5); % 画0.2水平线
% 
%     % 添加两条蓝色水平线
%     plot(xrange, [-0.05 -0.05], 'm-', 'LineWidth', 1.5); % 画-0.1水平线
%     plot(xrange, [0.05 0.05], 'm-', 'LineWidth', 1.5); % 画0.1水平线
% 
%     ax = gca;
%     ax.FontSize = 16; % 设置刻度尺文字大小
%     ax.FontName = 'Times New Roman'; % 设置刻度尺字体
%     ax.FontWeight = "bold";
%     ax.YLim = [-0.5 0.5]; % 自定义纵坐标范围
%     ax.XLim = [min(t) max(t)]; % 自动调整横坐标范围为时间序列的最小值和最大值
%     yticks([-1 -0.5 0 0.5 1]); % 自定义纵坐标刻度
% 
%     xlabel('Time(s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
%     ylabel('Normalized amplitude', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
%     title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
% 
%     hold off; % 关闭图形保持
% end



function plot_waveform_bs(t, data, title_text)
    plot(t, data); % 直接绘制波形
    hold on; % 保持当前图形，以便添加新的绘图元素
    
    % 添加两条红色水平线
    xrange = [min(t) max(t)]; % 获取x轴范围
    plot(xrange, [-0.2 -0.2], 'r-', 'LineWidth', 1.5); % 画-0.2水平线
    plot(xrange, [0.2 0.2], 'r-', 'LineWidth', 1.5); % 画0.2水平线
    
    % 添加两条蓝色水平线
    plot(xrange, [-0.02 -0.02], 'm-', 'LineWidth', 1.5); % 画-0.1水平线
    plot(xrange, [0.02 0.02], 'm-', 'LineWidth', 1.5); % 画0.1水平线
    
    ax = gca;
    ax.FontSize = 16; % 设置刻度尺文字大小
    ax.FontName = 'Times New Roman'; % 设置刻度尺字体
    ax.FontWeight = "bold";
    ax.YLim = [-0.5 0.5]; % 自定义纵坐标范围
    ax.XLim = [min(t) max(t)]; % 自动调整横坐标范围为时间序列的最小值和最大值
    
    % 设置网格
    ax.XGrid = 'on'; % 打开X轴网格
    ax.YGrid = 'off'; % 关闭Y轴网格
    % 设置网格间距为5
    start_x = ceil(min(t)/5)*5; % 确保起始点是5的倍数
    end_x = floor(max(t)/5)*5; % 确保结束点是5的倍数
    ax.XTick = start_x:5:end_x; % 设置X轴刻度间隔为5
    
    yticks([-1 -0.5 0 0.5 1]); % 自定义纵坐标刻度
    xlabel('Time(s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Normalized amplitude', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    
    hold off; % 关闭图形保持
end








