function plot_thresholded_waveform(t, data, title_text)
    plot(t, data); % 直接绘制波形
    ax = gca;
    ax.FontSize = 16; % 设置刻度尺文字大小
    ax.FontName = 'Times New Roman'; % 设置刻度尺字体
    ax.FontWeight = "bold";
    ax.YLim = [-0.1 0.1]; % 自定义纵坐标范围
    ax.XLim = [min(t) max(t)]; % 自动调整横坐标范围为时间序列的最小值和最大值
    yticks([-0.1 -0.05 0 0.05 0.1]); % 自定义纵坐标刻度
    xlabel('Time(s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Normalized amplitude', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
end


% function plot_thresholded_waveform(t, data, threshold, title_text)
%     % 找到超出和未超出阈值的索引
%     overThresholdIndices = abs(data) > threshold;
%     underThresholdIndices = ~overThresholdIndices;
% 
%     % 绘制数据
%     hold on;
%     % 正常绘制未超出阈值的部分，使用实线
%     plot(t(underThresholdIndices), data(underThresholdIndices));
% 
%     % 用红色星号标注出超出阈值的部分，避免连线
%     plot(t(overThresholdIndices), data(overThresholdIndices), 'r*');
% 
%     hold off;
% 
%     % 设置绘图格式
%     ax = gca;
%     ax.FontSize = 16; % 设置刻度尺文字大小
%     ax.FontName = 'Times New Roman'; % 设置刻度尺字体
%     ax.FontWeight = "bold";
%     ax.YLim = [-0.1 0.1]; % 自定义纵坐标范围
%     ax.XLim = [min(t) max(t)]; % 自动调整横坐标范围为时间序列的最小值和最大值
%     yticks([-0.1 -0.05 0 0.05 0.1]); % 自定义纵坐标刻度
%     xlabel('Time(s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
%     ylabel('Amplitude', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
%     title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
%     legend('Within Threshold', 'Exceeding Threshold');
% 
%     % 恢复图形的所有边框
%     box on;
% end
